#!/usr/bin/env python3
"""
Installation script for Kafkaesque Motif Analyzer dependencies.
"""

import subprocess
import sys
import os

def install_package(package):
    """Install a package using pip."""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def download_spacy_model():
    """Download spaCy English model."""
    try:
        subprocess.check_call([sys.executable, "-m", "spacy", "download", "en_core_web_sm"])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    """Install all required dependencies."""
    print("Installing Kafkaesque Motif Analyzer Dependencies")
    print("=" * 50)
    
    # Core packages
    packages = [
        "numpy>=1.21.0",
        "pandas>=1.3.0", 
        "tqdm>=4.62.0",
        "gensim>=4.1.0",
        "scipy>=1.7.0",
        "scikit-learn>=1.0.0"
    ]
    
    # Try to install spaCy first
    print("Installing spaCy...")
    if install_package("spacy>=3.4.0"):
        print("✓ spaCy installed successfully")
        
        print("Downloading spaCy English model...")
        if download_spacy_model():
            print("✓ spaCy English model downloaded successfully")
        else:
            print("⚠ Failed to download spaCy model, will fallback to NLTK")
    else:
        print("⚠ Failed to install spaCy, will use NLTK fallback")
    
    # Install NLTK as fallback
    print("Installing NLTK (fallback)...")
    if install_package("nltk>=3.7"):
        print("✓ NLTK installed successfully")
    else:
        print("✗ Failed to install NLTK")
        return False
    
    # Install other packages
    print("Installing other dependencies...")
    for package in packages:
        print(f"Installing {package}...")
        if install_package(package):
            print(f"✓ {package} installed successfully")
        else:
            print(f"✗ Failed to install {package}")
            return False
    
    print("\n" + "=" * 50)
    print("Installation complete!")
    print("You can now run: python kafka_motif_analyzer.py")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
