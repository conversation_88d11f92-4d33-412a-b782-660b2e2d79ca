{"analysis_config": {"corpus_dir": "corpus", "output_dir": "outputs", "num_topics": 10, "passes": 20, "iterations": 400, "alpha": "auto", "beta": "auto", "min_tokens_per_doc": 50, "max_tokens_per_doc": 10000, "min_word_freq": 5, "max_word_freq": 0.8, "random_state": 42, "coherence_measure": "c_v", "top_words_per_topic": 15, "chunk_size": 2000, "eval_every": null}, "corpus_stats": {"total_documents": 8, "total_unique_words": 198, "avg_doc_length": 2950.25, "total_kafka_keywords_found": 281, "unique_kafka_keywords_found": 22}, "model_performance": {"coherence_score": 0.3890300331733066, "num_topics": 10}, "top_topics": [{"topic_id": 0, "keywords": ["man", "hang", "case", "name", "group"]}, {"topic_id": 1, "keywords": ["send", "search", "short", "shoot", "service"]}, {"topic_id": 2, "keywords": ["end", "guard", "document", "rifle", "credit"]}, {"topic_id": 3, "keywords": ["room", "right", "look", "office", "way"]}, {"topic_id": 4, "keywords": ["end", "person", "credit", "agent", "arrest"]}], "top_kafka_keywords": [{"keyword": "control", "frequency": 104}, {"keyword": "power", "frequency": 49}, {"keyword": "weird", "frequency": 27}, {"keyword": "change", "frequency": 25}, {"keyword": "system", "frequency": 14}, {"keyword": "identity", "frequency": 14}, {"keyword": "authority", "frequency": 8}, {"keyword": "reality", "frequency": 6}, {"keyword": "maze", "frequency": 5}, {"keyword": "truth", "frequency": 5}]}