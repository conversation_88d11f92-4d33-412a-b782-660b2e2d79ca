# Kafkaesque Motif Analyzer

A Python script that processes game walkthroughs and wiki articles to identify Kafkaesque motifs using topic modeling with Latent Dirichlet Allocation (LDA).

## Features

- **Text Cleaning**: Removes ASCII art, button commands, and non-narrative content
- **Advanced Preprocessing**: Uses spaCy (with NLTK fallback) for tokenization and lemmatization
- **Topic Modeling**: Implements LDA with configurable parameters
- **Kafkaesque Analysis**: Tracks predefined keywords related to <PERSON><PERSON><PERSON>'s themes
- **Comprehensive Output**: Exports results in multiple formats (CSV, JSON)
- **Progress Tracking**: Uses tqdm for progress bars during processing

## Installation

1. Install Python dependencies:
```bash
pip install -r requirements.txt
```

2. Download spaCy language model (recommended):
```bash
python -m spacy download en_core_web_sm
```

## Directory Structure

```
project/
├── kafka_motif_analyzer.py    # Main script
├── requirements.txt           # Dependencies
├── corpus/                   # Input directory
│   ├── game1/
│   │   ├── walkthrough.txt
│   │   └── wiki_article.txt
│   └── game2/
│       └── guide.txt
└── outputs/                  # Results directory (created automatically)
    ├── topics.csv           # Topic keywords
    ├── doc_topics.csv       # Document-topic distributions
    ├── kafka_seed_hits.csv  # Kafkaesque keyword frequencies
    └── summary.json         # Analysis overview
```

## Usage

1. **Prepare your corpus**: Place .txt files in the `corpus/` directory, organized by game:
   ```
   corpus/
   ├── the_trial_game/
   │   ├── walkthrough.txt
   │   └── wiki.txt
   └── metamorphosis_adventure/
       └── guide.txt
   ```

2. **Run the analysis**:
   ```bash
   python kafka_motif_analyzer.py
   ```

3. **Check results** in the `outputs/` directory

## Configuration

Modify the `CONFIG` dictionary at the top of the script to adjust parameters:

```python
CONFIG = {
    'corpus_dir': 'corpus',           # Input directory
    'output_dir': 'outputs',          # Output directory
    'num_topics': 10,                 # Number of LDA topics
    'passes': 20,                     # LDA training passes
    'min_tokens_per_doc': 50,         # Minimum document length
    'min_word_freq': 5,               # Minimum word frequency
    'max_word_freq': 0.8,             # Maximum word frequency (ratio)
    # ... more parameters
}
```

## Output Files

### topics.csv
Lists all discovered topics with their top keywords:
- `topic_id`: Topic identifier
- `keywords`: Top keywords for the topic
- `top_keyword`: Most important keyword
- `avg_weight`: Average keyword weight

### doc_topics.csv
Shows topic distribution for each document:
- `file_path`: Document path
- `game`: Game name
- `dominant_topic`: Most prominent topic
- `topic_0`, `topic_1`, etc.: Weight for each topic

### kafka_seed_hits.csv
Frequency of predefined Kafkaesque keywords:
- `keyword`: Kafkaesque term
- `frequency`: Occurrence count

### summary.json
Analysis overview including:
- Configuration used
- Corpus statistics
- Model performance metrics
- Top topics and keywords

## Kafkaesque Keywords

The script tracks these thematic keywords:
- **Bureaucracy**: bureaucracy, administration, system
- **Guilt & Shame**: guilt, shame, blame
- **Absurdity**: absurd, meaningless, pointless
- **Alienation**: isolation, lonely, alienated
- **Anxiety**: anxiety, paranoia, fear
- **Transformation**: metamorphosis, change, mutation
- **Authority**: power, control, oppression
- **And more...**

## Text Cleaning Features

The script automatically removes:
- ASCII art and decorative elements
- Button commands ("Press X", "Hold L1")
- UI instructions and menu navigation
- Lines with mostly non-alphabetic characters
- Gaming-specific stopwords

## Dependencies

- **numpy**: Numerical computations
- **pandas**: Data manipulation and export
- **tqdm**: Progress bars
- **gensim**: Topic modeling with LDA
- **spacy**: Advanced NLP (recommended)
- **nltk**: NLP fallback option

## Example Output

```
Kafkaesque Motif Analyzer
==================================================
Configuration:
  corpus_dir: corpus
  num_topics: 10
  passes: 20
  ...

Loading documents...
Processing files: 100%|████████| 15/15 [00:02<00:00,  6.23it/s]
Loaded 15 documents

Creating dictionary and corpus...
Dictionary size: 1247
Corpus size: 15

Training LDA model...
Calculating coherence score...
Coherence score: 0.4523

Analyzing Kafkaesque keywords...
Exporting results...
Results exported to outputs/
- topics.csv: 10 topics
- doc_topics.csv: 15 documents
- kafka_seed_hits.csv: 23 Kafka keywords
- summary.json: Analysis overview

==================================================
ANALYSIS COMPLETE
==================================================
```

## Troubleshooting

1. **No .txt files found**: Ensure files are in `corpus/` directory with `.txt` extension
2. **spaCy model missing**: Run `python -m spacy download en_core_web_sm`
3. **Memory issues**: Reduce `num_topics` or `max_tokens_per_doc` in CONFIG
4. **Low coherence score**: Try adjusting `num_topics`, `passes`, or filtering parameters
