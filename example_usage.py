#!/usr/bin/env python3
"""
Example usage of the Kafkaesque Motif Analyzer with custom configuration.
"""

from kafka_motif_analyzer import KafkaMotifAnalyzer, CONFIG
import json

def run_custom_analysis():
    """Run analysis with custom configuration."""
    
    # Custom configuration for smaller dataset or testing
    custom_config = CONFIG.copy()
    custom_config.update({
        'num_topics': 5,           # Fewer topics for small dataset
        'passes': 10,              # Fewer passes for faster processing
        'min_tokens_per_doc': 20,  # Lower threshold for small documents
        'min_word_freq': 2,        # Lower frequency threshold
        'top_words_per_topic': 10, # Fewer words per topic
    })
    
    print("Running Kafkaesque Motif Analysis with custom configuration:")
    print(json.dumps(custom_config, indent=2))
    print()
    
    # Initialize and run analyzer
    analyzer = KafkaMotifAnalyzer(custom_config)
    analyzer.run_analysis()
    
    # Additional analysis
    print("\nAdditional Analysis:")
    print("-" * 30)
    
    # Show topic coherence
    print(f"Model coherence: {analyzer.coherence_score:.4f}")
    
    # Show most Kafkaesque documents
    if analyzer.doc_metadata:
        kafka_counts = analyzer.analyze_kafka_keywords()
        total_kafka_words = sum(kafka_counts.values())
        print(f"Total Kafkaesque keywords found: {total_kafka_words}")
        
        # Find documents with highest Kafka keyword density
        doc_kafka_scores = []
        for i, doc_tokens in enumerate(analyzer.documents):
            kafka_count = sum(1 for token in doc_tokens if token in kafka_counts)
            kafka_density = kafka_count / len(doc_tokens) if doc_tokens else 0
            doc_kafka_scores.append((i, kafka_density, analyzer.doc_metadata[i]['file_path']))
        
        # Sort by Kafka density
        doc_kafka_scores.sort(key=lambda x: x[1], reverse=True)
        
        print("\nMost Kafkaesque documents:")
        for i, (doc_idx, density, filepath) in enumerate(doc_kafka_scores[:5]):
            print(f"{i+1}. {filepath} (Kafka density: {density:.3f})")

def create_sample_corpus():
    """Create sample corpus for testing."""
    from pathlib import Path
    
    corpus_dir = Path("corpus")
    
    # Sample Kafkaesque texts
    samples = {
        "the_trial_game": {
            "walkthrough.txt": """
            You wake up in a sterile office building. The fluorescent lights buzz overhead as you 
            approach the reception desk. The clerk, without looking up, hands you Form 27-B. 
            "Fill this out completely," she says in a monotone voice. "Any errors will result 
            in starting the process over."
            
            The form asks for information you don't have: your case number, your assigned guilt 
            coefficient, your transformation authorization code. When you ask for help, the clerk 
            points to a sign: "All questions must be submitted in writing using Form 15-C."
            
            You realize you're trapped in an endless bureaucratic maze. Each form leads to another 
            form, each office to another office. The absurdity of the situation becomes clear as 
            you watch other people, hollow-eyed and defeated, shuffling between departments with 
            stacks of papers.
            
            The anxiety builds as you understand that there is no escape from this administrative 
            nightmare. You are guilty of something, but no one will tell you what. The system 
            has already judged you, and your only choice is to submit to its labyrinthine 
            procedures.
            """,
            
            "character_analysis.txt": """
            The protagonist represents the modern individual caught in the machinery of 
            bureaucracy. Their transformation from confident person to confused victim mirrors 
            Kafka's themes of alienation and powerlessness.
            
            The clerk embodies the faceless authority that enforces meaningless rules. Her 
            indifference to human suffering reflects the dehumanizing nature of institutional 
            power. The endless forms represent the absurd complexity of modern administrative 
            systems.
            
            The office building itself becomes a character - a maze-like structure that traps 
            souls and crushes hope. Its sterile environment strips away humanity, leaving only 
            the mechanical process of form-filling and rule-following.
            """
        },
        
        "metamorphosis_adventure": {
            "guide.txt": """
            Chapter 1: The Awakening
            
            You find yourself changed, different from before. Your reflection in the mirror 
            shows something alien, something that fills you with shame and self-loathing. 
            Your family's voices through the door carry tones of disgust and fear.
            
            The isolation is complete. You are trapped in your room, cut off from the world 
            you once knew. Every attempt to communicate results in horror from those who 
            once loved you. The transformation has made you a stranger in your own home.
            
            Your sister brings food, but even her kindness feels like pity. You sense her 
            growing resentment, her wish that you would simply disappear. The burden of your 
            existence weighs on everyone around you.
            
            As days pass, you realize that your identity has been completely erased. You are 
            no longer a person but a problem to be solved, a shame to be hidden. The 
            metamorphosis is not just physical but existential - you have become nothing.
            """
        }
    }
    
    # Create directories and files
    for game, files in samples.items():
        game_dir = corpus_dir / game
        game_dir.mkdir(parents=True, exist_ok=True)
        
        for filename, content in files.items():
            with open(game_dir / filename, 'w', encoding='utf-8') as f:
                f.write(content.strip())
    
    print(f"Created sample corpus in {corpus_dir}/")
    print("Sample files:")
    for game, files in samples.items():
        print(f"  {game}/")
        for filename in files.keys():
            print(f"    {filename}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "create-sample":
        create_sample_corpus()
    else:
        run_custom_analysis()
