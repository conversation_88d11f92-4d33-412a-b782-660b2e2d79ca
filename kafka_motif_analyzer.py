#!/usr/bin/env python3
"""
Kafkaesque Motif <PERSON> for Game Walkthroughs and Wiki Articles

This script processes text files from game walkthroughs and wiki articles to identify
Kafkaesque motifs using topic modeling with LDA (Latent Dirichlet Allocation).
"""

import os
import re
import json
import pandas as pd
from pathlib import Path
from collections import Counter, defaultdict
from typing import List, Dict, Tuple, Optional

import numpy as np
from tqdm import tqdm
import gensim
from gensim import corpora
from gensim.models import LdaModel
from gensim.models.coherencemodel import CoherenceModel

# Configuration dictionary
CONFIG = {
    'corpus_dir': 'corpus',
    'output_dir': 'outputs',
    'num_topics': 10,
    'passes': 20,
    'iterations': 400,
    'alpha': 'auto',
    'beta': 'auto',
    'min_tokens_per_doc': 50,
    'max_tokens_per_doc': 10000,
    'min_word_freq': 5,
    'max_word_freq': 0.8,
    'random_state': 42,
    'coherence_measure': 'c_v',
    'top_words_per_topic': 15,
    'chunk_size': 2000,
    'eval_every': None,
}

# Kafkaesque seed keywords for analysis
KAFKA_KEYWORDS = [
    'bureaucracy', 'bureaucratic', 'administration', 'administrative',
    'guilt', 'guilty', 'shame', 'shameful', 'blame',
    'absurd', 'absurdity', 'meaningless', 'pointless', 'futile',
    'alienation', 'alienated', 'isolation', 'isolated', 'lonely',
    'anxiety', 'anxious', 'paranoia', 'paranoid', 'fear',
    'transformation', 'metamorphosis', 'change', 'mutation',
    'trial', 'judgment', 'court', 'law', 'legal', 'justice',
    'maze', 'labyrinth', 'confusion', 'lost', 'trapped',
    'authority', 'power', 'control', 'oppression', 'system',
    'nightmare', 'surreal', 'bizarre', 'strange', 'weird',
    'helpless', 'powerless', 'victim', 'persecution',
    'identity', 'self', 'existence', 'reality', 'truth'
]

# Custom gaming stopwords
GAMING_STOPWORDS = [
    'player', 'game', 'level', 'mission', 'quest', 'save', 'load',
    'press', 'button', 'controller', 'keyboard', 'mouse', 'click',
    'menu', 'screen', 'interface', 'hud', 'ui', 'gui',
    'walkthrough', 'guide', 'tutorial', 'tip', 'hint',
    'achievement', 'trophy', 'score', 'point', 'xp', 'exp',
    'character', 'npc', 'enemy', 'boss', 'mob',
    'item', 'weapon', 'armor', 'equipment', 'inventory',
    'health', 'mana', 'stamina', 'energy', 'hp', 'mp',
    'attack', 'defend', 'move', 'action', 'skill', 'ability',
    'chapter', 'section', 'part', 'stage', 'area', 'zone',
    'cutscene', 'dialogue', 'conversation', 'text', 'message'
]

class KafkaMotifAnalyzer:
    def __init__(self, config: Dict = None):
        self.config = config or CONFIG
        self.documents = []
        self.doc_metadata = []
        self.dictionary = None
        self.corpus = None
        self.lda_model = None
        self.coherence_score = None
        
        # Initialize NLP components
        self.nlp = self._initialize_nlp()
        self.stopwords = self._get_stopwords()
        
        # Create output directory
        Path(self.config['output_dir']).mkdir(exist_ok=True)
    
    def _initialize_nlp(self):
        """Initialize spaCy or fallback to NLTK for text processing."""
        try:
            import spacy
            try:
                nlp = spacy.load("en_core_web_sm")
                print("Using spaCy for text processing")
                return nlp
            except OSError:
                print("spaCy model not found, downloading...")
                os.system("python -m spacy download en_core_web_sm")
                nlp = spacy.load("en_core_web_sm")
                return nlp
        except ImportError:
            print("spaCy not available, falling back to NLTK")
            try:
                import nltk
                from nltk.tokenize import word_tokenize
                from nltk.stem import WordNetLemmatizer
                from nltk.corpus import stopwords
                
                # Download required NLTK data
                nltk.download('punkt', quiet=True)
                nltk.download('wordnet', quiet=True)
                nltk.download('stopwords', quiet=True)
                nltk.download('averaged_perceptron_tagger', quiet=True)
                
                return 'nltk'
            except ImportError:
                raise ImportError("Neither spaCy nor NLTK is available. Please install one of them.")
    
    def _get_stopwords(self) -> set:
        """Get combined stopwords from NLTK and custom gaming terms."""
        try:
            from nltk.corpus import stopwords
            english_stopwords = set(stopwords.words('english'))
        except:
            # Fallback basic English stopwords
            english_stopwords = {
                'i', 'me', 'my', 'myself', 'we', 'our', 'ours', 'ourselves', 'you', 'your',
                'yours', 'yourself', 'yourselves', 'he', 'him', 'his', 'himself', 'she',
                'her', 'hers', 'herself', 'it', 'its', 'itself', 'they', 'them', 'their',
                'theirs', 'themselves', 'what', 'which', 'who', 'whom', 'this', 'that',
                'these', 'those', 'am', 'is', 'are', 'was', 'were', 'be', 'been', 'being',
                'have', 'has', 'had', 'having', 'do', 'does', 'did', 'doing', 'a', 'an',
                'the', 'and', 'but', 'if', 'or', 'because', 'as', 'until', 'while', 'of',
                'at', 'by', 'for', 'with', 'through', 'during', 'before', 'after', 'above',
                'below', 'up', 'down', 'in', 'out', 'on', 'off', 'over', 'under', 'again',
                'further', 'then', 'once'
            }
        
        return english_stopwords.union(set(GAMING_STOPWORDS))
    
    def clean_text(self, text: str) -> str:
        """Clean text by removing ASCII art, button commands, and non-narrative content."""
        lines = text.split('\n')
        cleaned_lines = []
        
        for line in lines:
            line = line.strip()
            
            # Skip empty lines
            if not line:
                continue
            
            # Remove ASCII art (lines with high ratio of special characters)
            special_chars = sum(1 for c in line if not c.isalnum() and not c.isspace())
            if len(line) > 0 and special_chars / len(line) > 0.5:
                continue
            
            # Remove button commands and UI instructions
            button_patterns = [
                r'press\s+[a-z0-9]+\b',
                r'hold\s+[a-z0-9]+\b',
                r'click\s+[a-z0-9]+\b',
                r'\b[lr][12]\b',
                r'\b[xy]\s*button\b',
                r'\bctrl\+[a-z]\b',
                r'\balt\+[a-z]\b',
                r'\bshift\+[a-z]\b',
                r'\bsave\s+game\b',
                r'\bload\s+game\b',
                r'\bmenu\s*>\s*\w+',
                r'\[\w+\]',  # Remove bracketed commands
            ]
            
            skip_line = False
            for pattern in button_patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    skip_line = True
                    break
            
            if skip_line:
                continue
            
            # Keep lines that are mostly alphabetic (narrative content)
            alpha_chars = sum(1 for c in line if c.isalpha())
            if len(line) > 0 and alpha_chars / len(line) >= 0.6:
                cleaned_lines.append(line)
        
        return ' '.join(cleaned_lines)

    def preprocess_text(self, text: str) -> List[str]:
        """Preprocess text: lowercase, tokenize, lemmatize, remove stopwords."""
        text = text.lower()

        if isinstance(self.nlp, str) and self.nlp == 'nltk':
            # NLTK processing
            import nltk
            from nltk.tokenize import word_tokenize
            from nltk.stem import WordNetLemmatizer

            lemmatizer = WordNetLemmatizer()
            tokens = word_tokenize(text)
            tokens = [lemmatizer.lemmatize(token) for token in tokens
                     if token.isalpha() and len(token) > 2 and token not in self.stopwords]
        else:
            # spaCy processing
            doc = self.nlp(text)
            tokens = [token.lemma_ for token in doc
                     if token.is_alpha and len(token.text) > 2
                     and token.lemma_.lower() not in self.stopwords
                     and not token.is_stop]

        return tokens

    def load_documents(self) -> None:
        """Load and process all text files from the corpus directory."""
        corpus_path = Path(self.config['corpus_dir'])

        if not corpus_path.exists():
            raise FileNotFoundError(f"Corpus directory '{corpus_path}' not found")

        print("Loading documents...")

        # Find all .txt files recursively
        txt_files = list(corpus_path.rglob("*.txt"))

        if not txt_files:
            raise FileNotFoundError(f"No .txt files found in '{corpus_path}'")

        for file_path in tqdm(txt_files, desc="Processing files"):
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()

                # Clean and preprocess text
                cleaned_text = self.clean_text(content)
                if not cleaned_text.strip():
                    continue

                tokens = self.preprocess_text(cleaned_text)

                # Filter documents by token count
                if (len(tokens) >= self.config['min_tokens_per_doc'] and
                    len(tokens) <= self.config['max_tokens_per_doc']):

                    self.documents.append(tokens)

                    # Extract metadata
                    relative_path = file_path.relative_to(corpus_path)
                    game_name = relative_path.parts[0] if len(relative_path.parts) > 1 else "unknown"

                    self.doc_metadata.append({
                        'file_path': str(relative_path),
                        'game': game_name,
                        'filename': file_path.name,
                        'token_count': len(tokens)
                    })

            except Exception as e:
                print(f"Error processing {file_path}: {e}")
                continue

        print(f"Loaded {len(self.documents)} documents")

        if not self.documents:
            raise ValueError("No valid documents found after preprocessing")

    def create_dictionary_and_corpus(self) -> None:
        """Create gensim dictionary and corpus from processed documents."""
        print("Creating dictionary and corpus...")

        # Create dictionary
        self.dictionary = corpora.Dictionary(self.documents)

        # Filter extremes
        self.dictionary.filter_extremes(
            no_below=self.config['min_word_freq'],
            no_above=self.config['max_word_freq']
        )

        # Create corpus
        self.corpus = [self.dictionary.doc2bow(doc) for doc in self.documents]

        print(f"Dictionary size: {len(self.dictionary)}")
        print(f"Corpus size: {len(self.corpus)}")

    def train_lda_model(self) -> None:
        """Train LDA topic model."""
        print("Training LDA model...")

        self.lda_model = LdaModel(
            corpus=self.corpus,
            id2word=self.dictionary,
            num_topics=self.config['num_topics'],
            random_state=self.config['random_state'],
            passes=self.config['passes'],
            iterations=self.config['iterations'],
            alpha=self.config['alpha'],
            eta=self.config['beta'],
            chunksize=self.config['chunk_size'],
            eval_every=self.config['eval_every']
        )

        # Calculate coherence score
        print("Calculating coherence score...")
        coherence_model = CoherenceModel(
            model=self.lda_model,
            texts=self.documents,
            dictionary=self.dictionary,
            coherence=self.config['coherence_measure']
        )
        self.coherence_score = coherence_model.get_coherence()

        print(f"Coherence score: {self.coherence_score:.4f}")

    def analyze_kafka_keywords(self) -> Dict[str, int]:
        """Analyze frequency of Kafkaesque keywords in the corpus."""
        print("Analyzing Kafkaesque keywords...")

        kafka_counts = Counter()

        for doc_tokens in self.documents:
            for token in doc_tokens:
                if token in KAFKA_KEYWORDS:
                    kafka_counts[token] += 1

        return dict(kafka_counts)

    def export_results(self) -> None:
        """Export all analysis results to the outputs directory."""
        output_dir = Path(self.config['output_dir'])

        print("Exporting results...")

        # 1. Export topics with top keywords
        topics_data = []
        for topic_id in range(self.config['num_topics']):
            topic_words = self.lda_model.show_topic(topic_id, topn=self.config['top_words_per_topic'])
            topic_keywords = [word for word, _ in topic_words]
            topic_weights = [weight for _, weight in topic_words]

            topics_data.append({
                'topic_id': topic_id,
                'keywords': ', '.join(topic_keywords),
                'top_keyword': topic_keywords[0] if topic_keywords else '',
                'avg_weight': np.mean(topic_weights) if topic_weights else 0
            })

        topics_df = pd.DataFrame(topics_data)
        topics_df.to_csv(output_dir / 'topics.csv', index=False)

        # 2. Export document-topic distributions
        doc_topics_data = []
        for doc_idx, doc_topics in enumerate(self.lda_model[self.corpus]):
            doc_info = self.doc_metadata[doc_idx].copy()

            # Get dominant topic
            if doc_topics:
                dominant_topic = max(doc_topics, key=lambda x: x[1])
                doc_info['dominant_topic'] = dominant_topic[0]
                doc_info['dominant_topic_weight'] = dominant_topic[1]
            else:
                doc_info['dominant_topic'] = -1
                doc_info['dominant_topic_weight'] = 0.0

            # Add all topic weights
            topic_weights = {f'topic_{i}': 0.0 for i in range(self.config['num_topics'])}
            for topic_id, weight in doc_topics:
                topic_weights[f'topic_{topic_id}'] = weight

            doc_info.update(topic_weights)
            doc_topics_data.append(doc_info)

        doc_topics_df = pd.DataFrame(doc_topics_data)
        doc_topics_df.to_csv(output_dir / 'doc_topics.csv', index=False)

        # 3. Export Kafka keyword analysis
        kafka_counts = self.analyze_kafka_keywords()
        kafka_df = pd.DataFrame([
            {'keyword': keyword, 'frequency': count}
            for keyword, count in sorted(kafka_counts.items(), key=lambda x: x[1], reverse=True)
        ])
        kafka_df.to_csv(output_dir / 'kafka_seed_hits.csv', index=False)

        # 4. Export summary
        summary = {
            'analysis_config': self.config,
            'corpus_stats': {
                'total_documents': len(self.documents),
                'total_unique_words': len(self.dictionary),
                'avg_doc_length': np.mean([len(doc) for doc in self.documents]),
                'total_kafka_keywords_found': sum(kafka_counts.values()),
                'unique_kafka_keywords_found': len(kafka_counts)
            },
            'model_performance': {
                'coherence_score': self.coherence_score,
                'num_topics': self.config['num_topics']
            },
            'top_topics': [
                {
                    'topic_id': i,
                    'keywords': [word for word, _ in self.lda_model.show_topic(i, topn=5)]
                }
                for i in range(min(5, self.config['num_topics']))
            ],
            'top_kafka_keywords': [
                {'keyword': k, 'frequency': v}
                for k, v in sorted(kafka_counts.items(), key=lambda x: x[1], reverse=True)[:10]
            ]
        }

        with open(output_dir / 'summary.json', 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)

        print(f"Results exported to {output_dir}/")
        print(f"- topics.csv: {len(topics_df)} topics")
        print(f"- doc_topics.csv: {len(doc_topics_df)} documents")
        print(f"- kafka_seed_hits.csv: {len(kafka_df)} Kafka keywords")
        print(f"- summary.json: Analysis overview")

    def run_analysis(self) -> None:
        """Run the complete Kafkaesque motif analysis pipeline."""
        try:
            self.load_documents()
            self.create_dictionary_and_corpus()
            self.train_lda_model()
            self.export_results()

            print("\n" + "="*50)
            print("ANALYSIS COMPLETE")
            print("="*50)
            print(f"Processed {len(self.documents)} documents")
            print(f"Found {len(self.dictionary)} unique terms")
            print(f"Coherence score: {self.coherence_score:.4f}")
            print(f"Results saved to: {self.config['output_dir']}/")

        except Exception as e:
            print(f"Error during analysis: {e}")
            raise


def main():
    """Main execution function."""
    print("Kafkaesque Motif Analyzer")
    print("="*50)

    # Display configuration
    print("Configuration:")
    for key, value in CONFIG.items():
        print(f"  {key}: {value}")
    print()

    # Check if corpus directory exists
    if not Path(CONFIG['corpus_dir']).exists():
        print(f"Creating example corpus directory structure at '{CONFIG['corpus_dir']}'...")
        example_path = Path(CONFIG['corpus_dir']) / "example_game"
        example_path.mkdir(parents=True, exist_ok=True)

        # Create example file
        example_content = """
This is an example walkthrough file. The protagonist finds themselves trapped in an endless
bureaucratic maze, filling out forms that lead to more forms. The sense of guilt and anxiety
grows as they realize the absurdity of their situation. Every door leads to another office,
every official sends them to another department. The transformation from hopeful citizen to
confused victim is complete. The system has consumed another soul in its labyrinthine
administrative nightmare.

To proceed, you must speak to the clerk at the desk. However, the clerk will only speak to
those who have the proper authorization form. To get the authorization form, you must visit
the Authorization Department on the third floor. But to access the third floor, you need
a floor pass from the Security Office in the basement.
"""

        with open(example_path / "example_walkthrough.txt", 'w', encoding='utf-8') as f:
            f.write(example_content)

        print(f"Created example file: {example_path / 'example_walkthrough.txt'}")
        print("Please add your .txt files to the corpus directory and run the script again.")
        return

    # Run analysis
    analyzer = KafkaMotifAnalyzer(CONFIG)
    analyzer.run_analysis()


if __name__ == "__main__":
    main()
