#!/usr/bin/env python3
"""
Install visualization dependencies for the LDA Topic Modeling Visualizer.
"""

import subprocess
import sys
from pathlib import Path

def install_requirements():
    """Install visualization requirements."""
    requirements_file = Path("visualization_requirements.txt")
    
    if not requirements_file.exists():
        print(f"Error: {requirements_file} not found")
        return False
    
    print("Installing visualization dependencies...")
    print("=" * 50)
    
    try:
        # Install requirements
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
        ])
        
        print("\n" + "=" * 50)
        print("✓ Visualization dependencies installed successfully!")
        print("You can now run: python main.py")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"\n✗ Error installing dependencies: {e}")
        print("Try installing manually with:")
        print(f"pip install -r {requirements_file}")
        return False

if __name__ == "__main__":
    install_requirements()
