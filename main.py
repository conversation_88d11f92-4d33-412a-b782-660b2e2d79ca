#!/usr/bin/env python3
"""
LDA Topic Modeling Visualization Script

This script generates visualizations from the outputs of an LDA topic modeling project,
specifically designed for the Kafkaesque Motif Analyzer results.

Generates:
- Wordclouds for each topic
- Bar chart of Kafka keyword frequencies
- Topic distribution by game (stacked bar chart and heatmap)

All plots are saved as .png files in outputs/plots/ directory.
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

# Try to import wordcloud, provide fallback if not available
try:
    from wordcloud import WordCloud
    WORDCLOUD_AVAILABLE = True
except ImportError:
    WORDCLOUD_AVAILABLE = False
    print("Warning: wordcloud library not available. Wordcloud plots will be skipped.")

# Configuration
VISUALIZATION_CONFIG = {
    'input_dir': 'outputs',
    'plots_dir': 'outputs/plots',
    'topics_file': 'topics.csv',
    'doc_topics_file': 'doc_topics.csv',
    'kafka_hits_file': 'kafka_seed_hits.csv',
    'figure_size': (12, 8),
    'dpi': 300,
    'style': 'whitegrid',
    'color_palette': 'viridis',
    'wordcloud_width': 800,
    'wordcloud_height': 400,
    'wordcloud_background': 'white',
    'font_size': 12,
    'title_size': 16
}

class LDAVisualizer:
    """Main class for generating LDA topic modeling visualizations."""

    def __init__(self, config: Dict = None):
        self.config = config or VISUALIZATION_CONFIG
        self.input_dir = Path(self.config['input_dir'])
        self.plots_dir = Path(self.config['plots_dir'])

        # Create plots directory
        self.plots_dir.mkdir(parents=True, exist_ok=True)

        # Set matplotlib and seaborn style
        plt.style.use('default')
        sns.set_style(self.config['style'])
        plt.rcParams['font.size'] = self.config['font_size']
        plt.rcParams['axes.titlesize'] = self.config['title_size']

        # Data containers
        self.topics_df = None
        self.doc_topics_df = None
        self.kafka_hits_df = None

    def load_data(self) -> bool:
        """Load all required CSV files with error handling."""
        print("Loading data files...")

        required_files = [
            (self.config['topics_file'], 'topics_df'),
            (self.config['doc_topics_file'], 'doc_topics_df'),
            (self.config['kafka_hits_file'], 'kafka_hits_df')
        ]

        missing_files = []

        for filename, attr_name in required_files:
            file_path = self.input_dir / filename

            if not file_path.exists():
                missing_files.append(str(file_path))
                continue

            try:
                df = pd.read_csv(file_path)
                setattr(self, attr_name, df)
                print(f"✓ Loaded {filename}: {len(df)} rows")

            except Exception as e:
                print(f"✗ Error loading {filename}: {e}")
                missing_files.append(str(file_path))

        if missing_files:
            print(f"\nMissing or corrupted files:")
            for file in missing_files:
                print(f"  - {file}")
            print("\nPlease ensure you have run the LDA analysis first to generate these files.")
            return False

        return True

    def generate_wordclouds(self) -> None:
        """Generate wordcloud images for each topic."""
        if not WORDCLOUD_AVAILABLE:
            print("Skipping wordclouds - wordcloud library not available")
            return

        if self.topics_df is None:
            print("Error: topics data not loaded")
            return

        print("Generating wordclouds for topics...")

        for _, topic_row in self.topics_df.iterrows():
            topic_id = topic_row['topic_id']
            keywords = topic_row['keywords']

            if pd.isna(keywords) or not keywords.strip():
                print(f"Warning: No keywords found for topic {topic_id}")
                continue

            try:
                # Create wordcloud
                wordcloud = WordCloud(
                    width=self.config['wordcloud_width'],
                    height=self.config['wordcloud_height'],
                    background_color=self.config['wordcloud_background'],
                    colormap=self.config['color_palette'],
                    max_words=50,
                    relative_scaling=0.5,
                    random_state=42
                ).generate(keywords)

                # Create plot
                plt.figure(figsize=(10, 5))
                plt.imshow(wordcloud, interpolation='bilinear')
                plt.axis('off')
                plt.title(f'Topic {topic_id} - Wordcloud',
                         fontsize=self.config['title_size'], pad=20)

                # Save plot
                output_path = self.plots_dir / f'topic_{topic_id}_wordcloud.png'
                plt.savefig(output_path, dpi=self.config['dpi'],
                           bbox_inches='tight', facecolor='white')
                plt.close()

                print(f"✓ Saved wordcloud for topic {topic_id}")

            except Exception as e:
                print(f"✗ Error generating wordcloud for topic {topic_id}: {e}")

    def plot_kafka_keyword_frequencies(self) -> None:
        """Generate bar chart of Kafka keyword frequencies."""
        if self.kafka_hits_df is None:
            print("Error: Kafka hits data not loaded")
            return

        print("Generating Kafka keyword frequency chart...")

        try:
            # Sort by frequency and take top keywords
            kafka_sorted = self.kafka_hits_df.sort_values('frequency', ascending=False)
            top_kafka = kafka_sorted.head(20)  # Top 20 keywords

            if len(top_kafka) == 0:
                print("Warning: No Kafka keywords found in data")
                return

            # Create bar plot
            plt.figure(figsize=self.config['figure_size'])

            bars = plt.bar(range(len(top_kafka)), top_kafka['frequency'],
                          color=plt.cm.viridis(np.linspace(0, 1, len(top_kafka))))

            plt.xlabel('Kafkaesque Keywords', fontsize=self.config['font_size'])
            plt.ylabel('Frequency', fontsize=self.config['font_size'])
            plt.title('Most Frequent Kafkaesque Keywords in Corpus',
                     fontsize=self.config['title_size'])

            # Set x-axis labels
            plt.xticks(range(len(top_kafka)), top_kafka['keyword'],
                      rotation=45, ha='right')

            # Add value labels on bars
            for i, bar in enumerate(bars):
                height = bar.get_height()
                plt.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                        f'{int(height)}', ha='center', va='bottom')

            plt.tight_layout()

            # Save plot
            output_path = self.plots_dir / 'kafka_keyword_frequencies.png'
            plt.savefig(output_path, dpi=self.config['dpi'], bbox_inches='tight')
            plt.close()

            print(f"✓ Saved Kafka keyword frequency chart")

        except Exception as e:
            print(f"✗ Error generating Kafka keyword chart: {e}")

    def plot_topic_distribution_by_game(self) -> None:
        """Generate topic distribution visualizations by game."""
        if self.doc_topics_df is None:
            print("Error: document topics data not loaded")
            return

        print("Generating topic distribution by game visualizations...")

        try:
            # Get topic columns (topic_0, topic_1, etc.)
            topic_cols = [col for col in self.doc_topics_df.columns if col.startswith('topic_')]

            if not topic_cols:
                print("Warning: No topic columns found in doc_topics.csv")
                return

            if 'game' not in self.doc_topics_df.columns:
                print("Warning: No 'game' column found in doc_topics.csv")
                return

            # Calculate average topic weights by game
            game_topic_means = self.doc_topics_df.groupby('game')[topic_cols].mean()

            # 1. Stacked Bar Chart
            self._create_stacked_bar_chart(game_topic_means, topic_cols)

            # 2. Heatmap
            self._create_topic_heatmap(game_topic_means, topic_cols)

        except Exception as e:
            print(f"✗ Error generating topic distribution plots: {e}")

    def _create_stacked_bar_chart(self, game_topic_means: pd.DataFrame, topic_cols: List[str]) -> None:
        """Create stacked bar chart of topic distributions by game."""
        try:
            plt.figure(figsize=self.config['figure_size'])

            # Create stacked bar chart
            game_topic_means.plot(kind='bar', stacked=True,
                                 colormap=self.config['color_palette'],
                                 figsize=self.config['figure_size'])

            plt.title('Topic Distribution by Game (Stacked Bar Chart)',
                     fontsize=self.config['title_size'])
            plt.xlabel('Games', fontsize=self.config['font_size'])
            plt.ylabel('Topic Weight', fontsize=self.config['font_size'])
            plt.legend(title='Topics', bbox_to_anchor=(1.05, 1), loc='upper left')
            plt.xticks(rotation=45, ha='right')
            plt.tight_layout()

            # Save plot
            output_path = self.plots_dir / 'topic_distribution_stacked_bar.png'
            plt.savefig(output_path, dpi=self.config['dpi'], bbox_inches='tight')
            plt.close()

            print("✓ Saved stacked bar chart")

        except Exception as e:
            print(f"✗ Error creating stacked bar chart: {e}")

    def _create_topic_heatmap(self, game_topic_means: pd.DataFrame, topic_cols: List[str]) -> None:
        """Create heatmap of topic distributions by game."""
        try:
            plt.figure(figsize=self.config['figure_size'])

            # Create heatmap
            sns.heatmap(game_topic_means.T, annot=True, fmt='.3f',
                       cmap=self.config['color_palette'],
                       cbar_kws={'label': 'Average Topic Weight'})

            plt.title('Topic Distribution by Game (Heatmap)',
                     fontsize=self.config['title_size'])
            plt.xlabel('Games', fontsize=self.config['font_size'])
            plt.ylabel('Topics', fontsize=self.config['font_size'])
            plt.xticks(rotation=45, ha='right')
            plt.yticks(rotation=0)
            plt.tight_layout()

            # Save plot
            output_path = self.plots_dir / 'topic_distribution_heatmap.png'
            plt.savefig(output_path, dpi=self.config['dpi'], bbox_inches='tight')
            plt.close()

            print("✓ Saved topic distribution heatmap")

        except Exception as e:
            print(f"✗ Error creating heatmap: {e}")

    def generate_summary_plot(self) -> None:
        """Generate a summary visualization combining key insights."""
        if self.topics_df is None or self.kafka_hits_df is None:
            print("Error: Required data not loaded for summary plot")
            return

        print("Generating summary visualization...")

        try:
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

            # 1. Top Kafka keywords (top-left)
            kafka_top = self.kafka_hits_df.nlargest(10, 'frequency')
            ax1.bar(range(len(kafka_top)), kafka_top['frequency'],
                   color=plt.cm.viridis(np.linspace(0, 1, len(kafka_top))))
            ax1.set_title('Top 10 Kafkaesque Keywords')
            ax1.set_xticks(range(len(kafka_top)))
            ax1.set_xticklabels(kafka_top['keyword'], rotation=45, ha='right')
            ax1.set_ylabel('Frequency')

            # 2. Topic keywords word frequency (top-right)
            if len(self.topics_df) > 0:
                # Count word frequencies across all topics
                all_keywords = ' '.join(self.topics_df['keywords'].fillna(''))
                word_freq = {}
                for word in all_keywords.split(', '):
                    word = word.strip()
                    if word:
                        word_freq[word] = word_freq.get(word, 0) + 1

                top_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:10]
                if top_words:
                    words, freqs = zip(*top_words)
                    ax2.bar(range(len(words)), freqs, color='skyblue')
                    ax2.set_title('Most Common Topic Keywords')
                    ax2.set_xticks(range(len(words)))
                    ax2.set_xticklabels(words, rotation=45, ha='right')
                    ax2.set_ylabel('Frequency Across Topics')

            # 3. Number of documents per game (bottom-left)
            if self.doc_topics_df is not None and 'game' in self.doc_topics_df.columns:
                game_counts = self.doc_topics_df['game'].value_counts()
                ax3.bar(range(len(game_counts)), game_counts.values, color='lightcoral')
                ax3.set_title('Documents per Game')
                ax3.set_xticks(range(len(game_counts)))
                ax3.set_xticklabels(game_counts.index, rotation=45, ha='right')
                ax3.set_ylabel('Number of Documents')

            # 4. Topic distribution statistics (bottom-right)
            if self.doc_topics_df is not None:
                topic_cols = [col for col in self.doc_topics_df.columns if col.startswith('topic_')]
                if topic_cols:
                    topic_means = self.doc_topics_df[topic_cols].mean()
                    ax4.bar(range(len(topic_means)), topic_means.values, color='lightgreen')
                    ax4.set_title('Average Topic Weights Across All Documents')
                    ax4.set_xticks(range(len(topic_means)))
                    ax4.set_xticklabels([f'Topic {i}' for i in range(len(topic_means))],
                                       rotation=45, ha='right')
                    ax4.set_ylabel('Average Weight')

            plt.tight_layout()

            # Save plot
            output_path = self.plots_dir / 'analysis_summary.png'
            plt.savefig(output_path, dpi=self.config['dpi'], bbox_inches='tight')
            plt.close()

            print("✓ Saved summary visualization")

        except Exception as e:
            print(f"✗ Error generating summary plot: {e}")

    def generate_all_visualizations(self) -> None:
        """Generate all visualizations in sequence."""
        print("="*60)
        print("LDA TOPIC MODELING VISUALIZATION GENERATOR")
        print("="*60)

        # Load data
        if not self.load_data():
            print("Failed to load required data files. Exiting.")
            return

        print(f"\nGenerating visualizations in: {self.plots_dir}")
        print("-" * 40)

        # Generate all plots
        self.generate_wordclouds()
        self.plot_kafka_keyword_frequencies()
        self.plot_topic_distribution_by_game()
        self.generate_summary_plot()

        print("-" * 40)
        print("VISUALIZATION GENERATION COMPLETE")
        print(f"All plots saved to: {self.plots_dir}")
        print("-" * 40)

        # List generated files
        plot_files = list(self.plots_dir.glob("*.png"))
        if plot_files:
            print(f"\nGenerated {len(plot_files)} visualization files:")
            for file in sorted(plot_files):
                print(f"  ✓ {file.name}")
        else:
            print("\nNo visualization files were generated.")


def main():
    """Main execution function."""
    # Create visualizer and generate all plots
    visualizer = LDAVisualizer()
    visualizer.generate_all_visualizations()


if __name__ == "__main__":
    main()