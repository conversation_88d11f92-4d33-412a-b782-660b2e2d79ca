# LDA Topic Modeling Visualization Script

This script generates comprehensive visualizations from the outputs of your Kafkaesque Motif Analyzer LDA topic modeling project.

## 📊 Generated Visualizations

### 1. **Wordclouds for Topics**
- Creates a wordcloud image for each discovered topic
- Uses topic keywords with visual emphasis on frequency
- Saved as: `topic_0_wordcloud.png`, `topic_1_wordcloud.png`, etc.

### 2. **Kafka Keyword Frequency Bar Chart**
- Shows the most frequent Kafkaesque keywords found in your corpus
- X-axis: Keywords, Y-axis: Frequency
- Saved as: `kafka_keyword_frequencies.png`

### 3. **Topic Distribution by Game**
- **Stacked Bar Chart**: Shows topic proportions for each game
- **Heatmap**: Matrix view of topic weights across games
- Saved as: `topic_distribution_stacked_bar.png` and `topic_distribution_heatmap.png`

### 4. **Analysis Summary**
- 4-panel overview combining key insights
- Top Kafka keywords, topic keywords, documents per game, average topic weights
- Saved as: `analysis_summary.png`

## 🚀 Quick Start

### 1. Install Dependencies
```bash
python install_visualization_deps.py
```

### 2. Run Visualizations
```bash
python main.py
```

## 📁 Required Input Files

The script expects these files in your `outputs/` directory:

- **`topics.csv`** - List of topics with keywords
- **`doc_topics.csv`** - Topic distribution per document with game labels  
- **`kafka_seed_hits.csv`** - Frequency of Kafka-related keywords

These files are automatically generated by running `kafka_motif_analyzer.py`.

## 📂 Output Structure

```
outputs/
├── topics.csv                           # Input files
├── doc_topics.csv
├── kafka_seed_hits.csv
└── plots/                               # Generated visualizations
    ├── topic_0_wordcloud.png
    ├── topic_1_wordcloud.png
    ├── ...
    ├── kafka_keyword_frequencies.png
    ├── topic_distribution_stacked_bar.png
    ├── topic_distribution_heatmap.png
    └── analysis_summary.png
```

## ⚙️ Configuration

Customize visualization settings by modifying the `VISUALIZATION_CONFIG` dictionary in `main.py`:

```python
VISUALIZATION_CONFIG = {
    'figure_size': (12, 8),           # Plot dimensions
    'dpi': 300,                       # Image resolution
    'color_palette': 'viridis',       # Color scheme
    'wordcloud_width': 800,           # Wordcloud dimensions
    'wordcloud_height': 400,
    'font_size': 12,                  # Text sizes
    'title_size': 16
}
```

## 🎨 Visualization Features

### Wordclouds
- Automatically sized based on keyword importance
- Consistent color scheme across all topics
- High-resolution output suitable for presentations

### Bar Charts
- Value labels on bars for precise reading
- Color gradients for visual appeal
- Rotated labels for readability

### Heatmaps
- Annotated cells showing exact values
- Color intensity represents topic strength
- Easy comparison across games

### Error Handling
- Graceful handling of missing files
- Informative error messages
- Continues processing even if some plots fail

## 🔧 Dependencies

- **matplotlib** - Core plotting functionality
- **seaborn** - Statistical visualizations and styling
- **wordcloud** - Word cloud generation
- **pandas** - Data manipulation
- **numpy** - Numerical operations

## 📋 Example Output

```
============================================================
LDA TOPIC MODELING VISUALIZATION GENERATOR
============================================================
Loading data files...
✓ Loaded topics.csv: 10 rows
✓ Loaded doc_topics.csv: 15 rows  
✓ Loaded kafka_seed_hits.csv: 23 rows

Generating visualizations in: outputs/plots
----------------------------------------
Generating wordclouds for topics...
✓ Saved wordcloud for topic 0
✓ Saved wordcloud for topic 1
...
Generating Kafka keyword frequency chart...
✓ Saved Kafka keyword frequency chart
Generating topic distribution by game visualizations...
✓ Saved stacked bar chart
✓ Saved topic distribution heatmap
Generating summary visualization...
✓ Saved summary visualization
----------------------------------------
VISUALIZATION GENERATION COMPLETE
All plots saved to: outputs/plots
----------------------------------------

Generated 13 visualization files:
  ✓ analysis_summary.png
  ✓ kafka_keyword_frequencies.png
  ✓ topic_0_wordcloud.png
  ✓ topic_1_wordcloud.png
  ...
```

## 🛠️ Troubleshooting

### Missing Input Files
- Ensure you've run `kafka_motif_analyzer.py` first
- Check that `outputs/` directory contains the required CSV files

### Import Errors
- Run `python install_visualization_deps.py` to install dependencies
- If wordcloud fails to install, visualizations will continue without wordclouds

### Memory Issues
- Reduce `dpi` setting for smaller file sizes
- Limit number of topics if processing large datasets

### Empty Plots
- Check that your CSV files contain data
- Verify column names match expected format (topic_0, topic_1, etc.)

## 🎓 For Academic Use

This visualization script is designed for university projects and research:

- **Publication-ready plots** with high DPI output
- **Comprehensive analysis** covering multiple aspects
- **Reproducible results** with configurable parameters
- **Professional styling** suitable for presentations and papers

Perfect for analyzing Kafkaesque themes in gaming literature and presenting findings visually!
