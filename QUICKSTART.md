# Quick Start Guide

## 1. Install Dependencies

**Option A: Automatic installation**
```bash
python install_dependencies.py
```

**Option B: Manual installation**
```bash
pip install -r requirements.txt
python -m spacy download en_core_web_sm
```

## 2. Prepare Your Data

Create the corpus directory structure:
```
corpus/
├── game1/
│   ├── walkthrough.txt
│   └── wiki.txt
└── game2/
    └── guide.txt
```

**Or create sample data:**
```bash
python example_usage.py create-sample
```

## 3. Run Analysis

**Basic analysis:**
```bash
python kafka_motif_analyzer.py
```

**Custom analysis:**
```bash
python example_usage.py
```

## 4. Check Results

Results will be saved in the `outputs/` directory:
- `topics.csv` - Discovered topics with keywords
- `doc_topics.csv` - Topic distribution per document  
- `kafka_seed_hits.csv` - Kafkaesque keyword frequencies
- `summary.json` - Analysis overview

## Configuration

Edit the `CONFIG` dictionary in `kafka_motif_analyzer.py`:

```python
CONFIG = {
    'num_topics': 10,        # Number of topics to discover
    'passes': 20,            # Training iterations
    'min_tokens_per_doc': 50, # Minimum document length
    # ... more options
}
```

## Troubleshooting

1. **Missing dependencies**: Run `python install_dependencies.py`
2. **No corpus found**: The script will create an example corpus on first run
3. **Low coherence score**: Try adjusting `num_topics` or `passes`
4. **Memory issues**: Reduce `max_tokens_per_doc` or `num_topics`

## Example Output

```
Kafkaesque Motif Analyzer
==================================================
Loaded 15 documents
Dictionary size: 1247
Coherence score: 0.4523
Results exported to outputs/
==================================================
ANALYSIS COMPLETE
==================================================
```
